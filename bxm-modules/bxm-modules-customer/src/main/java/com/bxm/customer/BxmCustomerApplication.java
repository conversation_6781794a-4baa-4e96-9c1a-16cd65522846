package com.bxm.customer;

import com.bxm.common.security.annotation.EnableCustomConfig;
import com.bxm.common.security.annotation.EnableJsFeignClients;
import com.bxm.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 客户模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableJsFeignClients
@SpringBootApplication
@EnableAsync
public class BxmCustomerApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(BxmCustomerApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  客户模块启动成功   ");
    }
}
