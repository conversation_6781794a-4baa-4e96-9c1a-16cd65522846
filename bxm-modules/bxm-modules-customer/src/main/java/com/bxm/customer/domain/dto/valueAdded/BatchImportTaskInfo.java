package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量导入任务信息存储DTO
 *
 * 用于在Redis中存储批量导入任务的详细信息
 * 包含任务状态、进度统计、操作信息等
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchImportTaskInfo {

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 任务状态：PENDING-待处理、PROCESSING-处理中、COMPLETED-已完成、FAILED-失败
     */
    private String taskStatus;

    /**
     * 操作类型
     */
    private ValueAddedBatchImportOperationType operation;

    /**
     * 操作类型描述
     */
    private String operationDescription;

    /**
     * 总数据量
     */
    private Integer totalCount;

    /**
     * 已处理数量
     */
    private Integer processedCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 异常数量
     */
    private Integer errorCount;

    /**
     * 成功的交付单编号列表
     */
    private List<String> successOrderNos;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 错误信息（任务失败时）
     */
    private String errorMessage;

    /**
     * 操作人用户名
     */
    private String operatorUsername;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新处理进度
     */
    public void updateProgress(int processed, int success, int error) {
        this.processedCount = processed;
        this.successCount = success;
        this.errorCount = error;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 标记任务开始
     */
    public void markAsStarted() {
        this.taskStatus = "PROCESSING";
        this.startTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 标记任务完成
     */
    public void markAsCompleted() {
        this.taskStatus = "COMPLETED";
        this.endTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.processingTimeMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    /**
     * 标记任务失败
     */
    public void markAsFailed(String errorMessage) {
        this.taskStatus = "FAILED";
        this.endTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.updateTime = LocalDateTime.now();
        if (this.startTime != null) {
            this.processingTimeMs = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    /**
     * 转换为进度DTO
     */
    public BatchImportProgressDTO toProgressDTO() {
        return BatchImportProgressDTO.builder()
                .batchNo(this.batchNo)
                .taskStatus(this.taskStatus)
                .operationDescription(this.operationDescription)
                .totalCount(this.totalCount)
                .processedCount(this.processedCount)
                .successCount(this.successCount)
                .errorCount(this.errorCount)
                .startTime(this.startTime)
                .endTime(this.endTime)
                .processingTimeMs(this.processingTimeMs)
                .errorMessage(this.errorMessage)
                .build();
    }
}
