package com.bxm.customer.domain.dto.valueAdded;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量导入统计数据
 *
 * 用于在Redis中存储批量导入的统计信息
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchImportStatistics {

    /**
     * 总数据量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 异常数量
     */
    private Integer errorCount;
}
